import React, { useMemo } from 'react';
import { getSecondaryActions } from '../../../config/menuItems';
import type { SecondaryAction } from '../../../config/menuItems';
import { isSelectionInEditableArea } from '../../utils/editableDetection';
import * as styles from './index.module.less';

interface SecondaryActionButtonsProps {
  menuItemId: string;
  onAction: (action: SecondaryAction) => void;
  disabled?: boolean;
  className?: string;
}

const SecondaryActionButtons: React.FC<SecondaryActionButtonsProps> = ({
  menuItemId,
  onAction,
  disabled = false,
  className = ''
}) => {
  // 动态过滤菜单项，基于当前选中内容是否在可编辑区域内
  const allActions = getSecondaryActions(menuItemId);
  const isInEditableArea = isSelectionInEditableArea();

  const secondaryActions = useMemo(() => {
    // 如果选中内容不在可编辑区域内，过滤掉编辑相关的菜单项
    if (!isInEditableArea) {
      return allActions.filter(action =>
        action.id !== 'insert-below' && action.id !== 'replace-original'
      );
    }

    // 如果在可编辑区域内，显示所有菜单项
    return allActions;
  }, [allActions, isInEditableArea]); // 依赖实际的状态变化

  if (secondaryActions.length === 0) {
    return null;
  }

  // 判断是否为主要操作（替换原文）
  const isPrimaryAction = (action: SecondaryAction): boolean => {
    return action.id === 'replace-original';
  };

  // 分离继续问按钮和其他按钮
  const continueAskingAction = secondaryActions.find(action => action.id === 'continue-asking');
  const otherActions = secondaryActions.filter(action => action.id !== 'continue-asking');

  return (
    <div className={`${styles.actionButtons} ${continueAskingAction ? styles.hasBothGroups : styles.rightOnly} ${className}`}>
      {/* 左侧：继续问按钮 */}
      {continueAskingAction && (
        <div className={styles.leftGroup}>
          <button
            key={continueAskingAction.id}
            className={`${styles.actionBtn} ${isPrimaryAction(continueAskingAction) ? styles.primaryBtn : ''}`}
            onClick={() => onAction(continueAskingAction)}
            disabled={disabled}
            title={continueAskingAction.label}
          >
            {continueAskingAction.label}
          </button>
        </div>
      )}

      {/* 右侧：其他操作按钮 */}
      {otherActions.length > 0 && (
        <div className={styles.rightGroup}>
          {otherActions.map((action) => (
            <button
              key={action.id}
              className={`${styles.actionBtn} ${isPrimaryAction(action) ? styles.primaryBtn : ''}`}
              onClick={() => onAction(action)}
              disabled={disabled}
              title={action.label}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SecondaryActionButtons;
